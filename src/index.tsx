import { createRoot } from 'react-dom/client';
import { App } from '@components/app';
import { GlobalConstants } from '@classes/constants';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import * as Sentry from '@sentry/react';
import '@styles/style.scss';
import {
    createRoutesFromChildren,
    matchRoutes,
    useLocation,
    useNavigationType,
} from 'react-router-dom';
import React from 'react';

export const render = () => {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const container = document.getElementById(GlobalConstants.MainRoot);
    const root = createRoot(container);
    root.render(<App />);
};

Sentry.init({
    dsn: 'http://29d39339c5e247218b1f342188cea5e5@localhost:8200/1',

    sendDefaultPii: true,
    integrations: [
        Sentry.reactRouterV6BrowserTracingIntegration({
            useEffect: React.useEffect,
            useLocation,
            useNavigationType,
            createRoutesFromChildren,
            matchRoutes,
        }),
    ],
    tracesSampleRate: 1.0,
});

render();
