import axios, { AxiosInstance } from 'axios';
import { TMetadata } from './responseModels/requestResult';
import { RequestResult } from './responseModels/requestResult';
import { GlobalConstants } from '@classes/constants';
import { ErrorHandlingManager } from '@classes/errorHandlingManager';
import * as Sentry from '@sentry/react';

class CRMAPIBase {
    protected _httpClient: AxiosInstance;
    protected _accessToken?: string;

    constructor(token?: string) {
        this._httpClient = axios.create({
            baseURL: GlobalConstants.BaseUrl,
            timeout: GlobalConstants.RequestTimeout,
        });
        if (token) {
            this._accessToken = token;
            this._httpClient.defaults.headers.common['Authorization'] =
                'Bearer ' + this._accessToken;
        }
    }

    /**
     * Отправка GET запроса
     * @param url url запроса
     * @param params Параметры для строки запроса
     * @param silent Заглушить уведомления
     * @returns Результат запроса
     */
    protected async get<T>(url: string, params?: any, silent = false): Promise<RequestResult<T>> {
        return this.request('GET', url, null, params, silent);
    }

    /**
     * Отправка POST запроса
     * @param url url запроса
     * @param data Параметры для тела запроса
     * @param params Параметры для строки запроса
     * @returns Результат запроса
     */
    protected async post<T>(url: string, data?: any, params?: any): Promise<RequestResult<T>> {
        return this.request('POST', url, data, params);
    }

    /**
     * Отправка PUT запроса
     * @param url url запроса
     * @param data Параметры для тела запроса
     * @param params Параметры для строки запроса
     * @returns Результат запроса
     */
    protected async put<T>(url: string, data?: any, params?: any): Promise<RequestResult<T>> {
        return this.request('PUT', url, data, params);
    }

    /**
     * Отправка DELETE запроса
     * @param url url запроса
     * @param params Параметры для тела запроса
     * @returns Результат запроса
     */
    protected async delete<T>(url: string, params?: any): Promise<RequestResult<T>> {
        return this.request('DELETE', url, null, params);
    }

    /**
     * Отправка Общего запроса
     * @param method Метод запроса
     * @param url url запроса
     * @param data Параметры для тела запроса
     * @param params Параметры для строки запроса
     * @param silent Заглушить уведомления
     * @returns Результат запроса
     */
    private async request<T>(
        method: string,
        url: string,
        data?: any,
        params?: any,
        silent = false,
    ): Promise<RequestResult<T>> {
        let result = new RequestResult<T>();
        try {
            const isFormData = data instanceof FormData;
            const response = await this._httpClient.request<T & { meta?: TMetadata }>({
                method: method,
                url: url,
                data: data,
                params: params,
                headers: isFormData ? {} : { 'Content-Type': 'application/json' },
            });
            result.statusCode = response.status;
            if (response.status >= 200 && response.status <= 300) {
                result.data = response.data;
            } else {
                result = CRMAPIBase.parseErrors<T>(response.status, response.data);
            }
        } catch (error) {
            Sentry.captureException(error);
            if (error.response) {
                if (error.response.status === 401 && window.location.pathname != '/login') {
                    window.location.href = '/login';
                }
                result = CRMAPIBase.parseErrors<T>(error.response.status, error.response.data);
            } else {
                result.statusCode = 500;
                result.errorMessages = [error.message || 'Ошибка сети'];
            }
        }

        if (!silent) this.throwIfError(result);

        return result;
    }

    /**
     * Парсинг ошибок
     * @param data Тело
     * @returns Расшифровка
     */
    public static parseErrors<T>(statusCode: number, data: any): RequestResult<T> {
        const apiError = ErrorHandlingManager.parseApiError(statusCode, data);
        return {
            statusCode: apiError.statusCode,
            errorCode: apiError.errorCode,
            errorMessages: apiError.errorMessages,
        };
    }

    /**
     * Проверяет, содержит ли результат запроса ошибки, и выбрасывает исключение, если это так
     * @param result Результат запроса для проверки
     * @throws Error если результат запроса содержит ошибки
     */
    protected throwIfError<T>(result: RequestResult<T>): void {
        if (result.errorMessages?.length || result.statusCode >= 400) {
            // Только создаем ошибку и выбрасываем ее, но не обрабатываем на этом уровне
            ErrorHandlingManager.createAndThrow({
                statusCode: result.statusCode,
                errorCode: result.errorCode,
                errorMessages: result.errorMessages,
            });
        }
    }
}

export { CRMAPIBase };
