variables:
    HUSKY: 0

stages:
    - codeql
# - publish
# - staging

# variables:
#   TAG_COMMIT: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHORT_SHA
#   TAG_LATEST: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:latest

codeql:
    stage: codeql
    image: node:22-alpine
    variables:
        ESLINT_CODE_QUALITY_REPORT: gl-codequality.json
    before_script:
        - corepack enable
        - pnpm install
    script:
        - pnpm format
        - pnpm lint --format gitlab
    artifacts:
        reports:
            codequality: gl-codequality.json
    rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    tags:
        - frontend
# publish:
#   stage: publish
#   image: docker:latest
#   services:
#     - docker:dind
#   before_script:
#     - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
#   script:
#     - docker build -t $TAG_LATEST -t $TAG_COMMIT .
#     - docker push $TAG_LATEST
#     - docker push $TAG_COMMIT
#   rules:
#     - if: $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_TAG == null
#   tags:
#     - frontend

# staging:
#   stage: staging
#   image: alpine:latest
#   before_script:
#     - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
#   script:
#     - chmod 400 $GITLAB_KEY
#     - apk add openssh-client
#     - ssh -i $GITLAB_KEY -o StrictHostKeyChecking=no $USER@$STAGING_TARGET "
#         cd $DEPLOY_DIR && docker pull $TAG_LATEST && docker compose down && docker compose up -d
#       "
#   rules:
#     - if: $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_TAG == null
#   tags:
#     - frontend
#   environment:
#     name: staging
#     url: http://$STAGING_TARGET
